<template>
  <div class="bigdata-template">
    <!-- 返回按钮 -->
    <div class="back-button">
      <el-button type="primary" icon="el-icon-arrow-left" @click="goBack">
        返回
      </el-button>
    </div>

    <!-- 加载动画 -->
    <div class="loading" v-show="loading">
      <div class="loadbox">
        <img src="~@/assets/images/bigdata/loading.gif" /> 页面加载中...
      </div>
    </div>

    <!-- 头部 -->
    <div class="head">
      <h1>大数据可视化系统数据分析通用模版</h1>
      <div class="time" id="showTime">
        {{ currentTime }}
      </div>
    </div>

    <!-- 主体内容 -->
    <div class="mainbox" style="margin-top: -18px;">
      <ul class="clearfix">
        <!-- 左侧列 -->
        <li>
          <div class="boxall" style="height: calc(55% - .15rem)">
            <div class="alltitle">
              商品销售排行
            </div>
            <div class="boxnav" id="echarts5" style="margin-bottom: 10px;">
              <div ref="chart5" style="width: 100%; height: 100%;"></div>
            </div>
          </div>
          <div class="boxall" style="height: calc(42% - .15rem)">
            <div class="alltitle">
              近七日会员活跃数
            </div>
            <div class="boxnav" id="echarts3">
              <div ref="chart3" style="width: 100%; height: 100%;"></div>
            </div>
          </div>
        </li>

        <!-- 中间列 -->
        <li>
          <div class="boxall" style="height: calc(15% - .15rem)">
            <ul class="row h100 clearfix">
              <li class="col-6" style="margin-top: -30px;margin-left: -90px;">
                <div class="sqzs h100">
                  <p>业绩总览</p>
                  <h1><span style="margin-top: 40px;margin-left: 60px;">{{ formatNumber(homeData.totalPay || 0) }}万</span></h1>
                </div>
              </li>
              <li class="col-6" >
                <ul class="row h100 clearfix" style="margin-right: -50px;">
                  <li class="col-4">
                    <div class="tit01">
                      今日订单
                    </div>
                    <div class="piebox" id="pe01">
                      <div class="pie-number">{{ homeData.todayOrder || 0 }}</div>
                    </div>
                  </li>
                  <li class="col-4">
                    <div class="tit01">
                      今日用户
                    </div>
                    <div class="piebox" id="pe02">
                      <div class="pie-number">{{ homeData.todayUser || 0 }}</div>
                    </div>
                  </li>
                  <li class="col-4">
                    <div class="tit01">
                      活跃用户
                    </div>
                    <div class="piebox" id="pe03">
                      <div class="pie-number">{{ homeData.todayActiveUser || 0 }}</div>
                    </div>
                  </li>
                </ul>
              </li>
            </ul>
          </div>
          <div class="boxall" style="height: calc(38% - .15rem)">
            <div class="alltitle">
              订单趋势分析
            </div>
            <div class="boxnav" id="echarts1">
              <div ref="chart1" style="width: 100%; height: 100%;"></div>
            </div>
          </div>
          <div class="boxall" style="height: calc(42% - .15rem)">
            <div class="alltitle">
              交易金额趋势
            </div>
            <div class="boxnav" id="echarts2">
              <div ref="chart2" style="width: 100%; height: 100%;"></div>
            </div>
          </div>
        </li>

        <!-- 右侧列 -->
        <li>
          <div class="boxall" style="height: calc(27% - .15rem)">
            <div class="alltitle">
              近七日订单数量
            </div>
            <div class="boxnav" id="echarts4" style="margin-bottom: -15px;">
              <div ref="chart4" style="width: 100%; height: 100%"></div>
            </div>
          </div>
          <div class="boxall" style="height: calc(36% - .15rem)">
            <div class="alltitle">
              数据统计表
            </div>
            <div class="boxnav" id="">
              <table border="0" cellspacing="0">
                <tbody>
                <tr>
                  <th></th>
                  <th>订单数</th>
                  <th>增长率</th>
                  <th>用户数</th>
                  <th>金额</th>
                </tr>
                <tr v-for="(item, index) in tableData" :key="index">
                  <th>{{ item.name }}</th>
                  <td>{{ item.orders }}</td>
                  <td>{{ item.growth }}%</td>
                  <td>{{ item.users }}</td>
                  <td>{{ item.amount }}</td>
                </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div class="boxall" style="height: calc(37% - .15rem)">
            <div class="alltitle">
              用户分布
            </div>
            <div class="boxnav" id="echarts6">
              <div ref="chart6" style="width: 100%; height: 100%;"></div>
            </div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import echarts from 'echarts'
import { getHomeData, getStatisticData } from "@/api/home";
import { getMainData, getTopData } from "@/api/statistic";
import { getNumDayTime } from "@/utils/fuint";

export default {
  name: 'BigDataTemplate',
  data() {
    return {
      loading: true,
      currentTime: '',
      homeData: {
        todayOrder: 0,
        todayUser: 0,
        todayActiveUser: 0,
        totalPay: 0,
        totalUser: 0,
        totalOrder: 0,
        totalPayUser: 0
      },
      mainData: {
        orderCount: 0,
        payAmount: 0,
        userCount: 0,
        activeUserCount: 0,
        totalUserCount: 0,
        totalPayAmount: 0,
        totalOrderCount: 0,
        totalPayUserCount: 0
      },
      chartData1: [], // 近七日订单数量
      chartData2: [], // 近七日会员活跃数
      goodsList: [], // 商品销售排行
      memberList: [], // 用户消费排行
      tableData: [
        { name: '今日', orders: 0, growth: 0, users: 0, amount: 0 },
        { name: '昨日', orders: 0, growth: 0, users: 0, amount: 0 },
        { name: '本周', orders: 0, growth: 0, users: 0, amount: 0 },
        { name: '本月', orders: 0, growth: 0, users: 0, amount: 0 }
      ],
      charts: {}
    }
  },
  mounted() {
    this.initTime()
    this.loadData()

    // 添加窗口大小改变时重绘所有图表的事件监听
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    // 销毁图表实例
    Object.values(this.charts).forEach(chart => {
      if (chart) {
        chart.dispose()
      }
    })
    
    // 移除窗口大小改变事件监听
    window.removeEventListener('resize', this.handleResize);
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },
    // 初始化时间显示
    initTime() {
      this.updateTime()
      setInterval(this.updateTime, 1000)
    },
    updateTime() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const hours = String(now.getHours()).padStart(2, '0')
      const minutes = String(now.getMinutes()).padStart(2, '0')
      const seconds = String(now.getSeconds()).padStart(2, '0')
      this.currentTime = `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`
    },
    // 格式化数字
    formatNumber(num) {
      return (num / 10000).toFixed(1)
    },
    // 加载所有数据
    async loadData() {
      this.loading = true
      try {
        await Promise.all([
          this.getHomeData(),
          this.getMainData(),
          this.getChartsData(),
          this.getTopData()
        ])
        // 数据加载完成后初始化图表
        this.$nextTick(() => {
          this.initCharts()
          this.loading = false
        })
      } catch (error) {
        console.error('数据加载失败:', error)
        this.loading = false
        // 即使数据加载失败也初始化图表，使用默认数据
        this.$nextTick(() => {
          this.initCharts()
        })
      }
    },
    // 获取首页数据
    async getHomeData() {
      try {
        const response = await getHomeData()
        this.homeData = response.data
        this.updateTableData()
      } catch (error) {
        console.error('获取首页数据失败:', error)
      }
    },
    // 获取主要统计数据
    async getMainData() {
      try {
        const param = {
          storeId: 0,
          startTime: getNumDayTime(30),
          endTime: getNumDayTime(0)
        }
        const response = await getMainData(param)
        this.mainData = response.data
        this.updateTableData()
      } catch (error) {
        console.error('获取主要统计数据失败:', error)
      }
    },
    // 获取图表数据
    async getChartsData() {
      try {
        const response = await getStatisticData({ tag: 'order,user_active' })
        const data = response.data
        const labelData1 = data.data[0] ? data.data[0] : []
        const labelData2 = data.data[1] ? data.data[1] : []

        this.chartData1 = []
        this.chartData2 = []

        data.labels.forEach((label, index) => {
          const value1 = labelData1[index] ? labelData1[index] : 0
          const value2 = labelData2[index] ? labelData2[index] : 0
          this.chartData1.push({ name: label, value: value1 })
          this.chartData2.push({ name: label, value: value2 })
        })
      } catch (error) {
        console.error('获取图表数据失败:', error)
      }
    },
    // 获取排行榜数据
    async getTopData() {
      try {
        const param = {
          storeId: 0,
          startTime: getNumDayTime(30),
          endTime: getNumDayTime(0)
        }
        const response = await getTopData(param)
        this.goodsList = response.data.goodsList || []
        this.memberList = response.data.memberList || []
      } catch (error) {
        console.error('获取排行榜数据失败:', error)
      }
    },
    // 更新表格数据
    updateTableData() {
      this.tableData = [
        {
          name: '今日',
          orders: this.homeData.todayOrder || 0,
          growth: this.calculateGrowthRate(this.homeData.todayOrder, this.homeData.totalOrder),
          users: this.homeData.todayUser || 0,
          amount: this.homeData.todayPay ? (this.homeData.todayPay / 10000).toFixed(1) : 0
        },
        {
          name: '总计',
          orders: this.homeData.totalOrder || 0,
          growth: 100,
          users: this.homeData.totalUser || 0,
          amount: this.homeData.totalPay ? (this.homeData.totalPay / 10000).toFixed(1) : 0
        },
        {
          name: '活跃',
          orders: this.mainData.orderCount || 0,
          growth: this.calculateGrowthRate(this.mainData.orderCount, this.homeData.totalOrder),
          users: this.homeData.todayActiveUser || 0,
          amount: this.mainData.payAmount ? (this.mainData.payAmount / 10000).toFixed(1) : 0
        },
        {
          name: '支付',
          orders: this.mainData.totalOrderCount || 0,
          growth: this.calculateGrowthRate(this.mainData.totalOrderCount, this.homeData.totalOrder),
          users: this.homeData.totalPayUser || 0,
          amount: this.mainData.totalPayAmount ? (this.mainData.totalPayAmount / 10000).toFixed(1) : 0
        }
      ]
    },
    // 计算增长率
    calculateGrowthRate(current, total) {
      if (!total || total === 0) return 0
      return ((current / total) * 100).toFixed(1)
    },
    // 生成月度数据
    generateMonthlyData(baseValue, type = 'positive') {
      if (!baseValue) return null

      const monthlyData = []
      const base = baseValue / 100000 // 转换为合适的显示单位

      for (let i = 0; i < 12; i++) {
        let value
        if (type === 'positive') {
          // 生成正向波动数据
          value = base * (0.8 + Math.random() * 0.4) // 80%-120%的波动
        } else if (type === 'mixed') {
          // 生成混合波动数据（包含负值）
          value = base * (0.5 + Math.random() * 1.0) - base * 0.3 // 可能为负值
        }
        monthlyData.push(parseFloat(value.toFixed(2)))
      }

      return monthlyData
    },
    // 窗口大小改变时重绘所有图表
    handleResize() {
      Object.values(this.charts).forEach(chart => {
        if (chart) {
          chart.resize();
        }
      });
    },
    // 初始化所有图表
    initCharts() {
      this.$nextTick(() => {
        this.initChart5() // 商品销售排行（左上）
        this.initChart3() // 近七日会员活跃数（左下）
        this.initChart1() // 订单趋势分析（中间）
        this.initChart2() // 交易金额趋势（中间下）
        this.initChart4() // 近七日订单数量（右上）
        this.initChart6() // 用户分布（右下）
      })
    },
    // 图表1 - 订单趋势分析（混合图表）
    initChart1() {
      this.charts.chart1 = echarts.init(this.$refs.chart1)
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          textStyle: { fontSize: 14 }
        },
        grid: {
          top: '20%',
          right: '50',
          bottom: '20',
          left: '30'
        },
        legend: {
          data: ['订单量', '销售额', '用户数', '订单增长率', '销售增长率', '用户增长率'],
          right: 'center',
          width: '100%',
          textStyle: { color: '#fff', fontSize: 14 },
          itemWidth: 15,
          itemHeight: 10
        },
        xAxis: [{
          type: 'category',
          data: ['2020', '2021', '2022', '2023'],
          axisLine: { lineStyle: { color: 'rgba(255,255,255,.1)' } },
          axisLabel: { textStyle: { color: 'rgba(255,255,255,.7)', fontSize: '16' } }
        }],
        yAxis: [
          {
            type: 'value',
            name: '单位万',
            nameTextStyle: { color: 'rgba(255,255,255,.6)', fontSize: 14 },
            axisTick: { show: false },
            splitLine: { show: false },
            axisLabel: {
              show: true,
              fontSize: 16,
              color: 'rgba(255,255,255,.6)'
            },
            axisLine: {
              min: 0,
              max: 10,
              lineStyle: { color: 'rgba(255,255,255,.1)' }
            }
          },
          {
            type: 'value',
            name: '增速',
            nameTextStyle: { color: 'rgba(255,255,255,.6)', fontSize: 14 },
            show: true,
            axisLabel: {
              show: true,
              fontSize: 16,
              formatter: '{value} %',
              color: 'rgba(255,255,255,.6)'
            },
            axisTick: { show: false },
            axisLine: { lineStyle: { color: 'rgba(255,255,255,.1)' } },
            splitLine: { show: true, lineStyle: { color: 'rgba(255,255,255,.1)' } }
          }
        ],
        series: [
          {
            name: '订单量',
            type: 'bar',
            data: [
              (this.homeData.totalOrder / 10000 * 0.8) || 36.6,
              (this.homeData.totalOrder / 10000 * 0.85) || 38.80,
              (this.homeData.totalOrder / 10000 * 0.9) || 40.84,
              (this.homeData.totalOrder / 10000) || 41.60
            ],
            barWidth: '15%',
            itemStyle: {
              normal: {
                barBorderRadius: 15,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#8bd46e' },
                  { offset: 1, color: '#09bcb7' }
                ])
              }
            },
            barGap: '0.2',
            label: {
              show: true,
              position: 'top',
              fontSize: 12,
              color: 'rgba(255,255,255,0.7)'
            }
          },
          {
            name: '销售额',
            type: 'bar',
            data: [
              (this.homeData.totalPay / 100000 * 0.7) || 14.8,
              (this.homeData.totalPay / 100000 * 0.75) || 14.1,
              (this.homeData.totalPay / 100000 * 0.85) || 15,
              (this.homeData.totalPay / 100000) || 16.30
            ],
            barWidth: '15%',
            itemStyle: {
              normal: {
                barBorderRadius: 15,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#248ff7' },
                  { offset: 1, color: '#6851f1' }
                ])
              }
            },
            barGap: '0.2'
          },
          {
            name: '用户数',
            type: 'bar',
            data: [
              (this.homeData.totalUser / 10000 * 0.8) || 9.2,
              (this.homeData.totalUser / 10000 * 0.85) || 9.1,
              (this.homeData.totalUser / 10000 * 0.9) || 9.85,
              (this.homeData.totalUser / 10000) || 8.9
            ],
            barWidth: '15%',
            itemStyle: {
              normal: {
                barBorderRadius: 15,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#fccb05' },
                  { offset: 1, color: '#f5804d' }
                ])
              }
            },
            barGap: '0.2'
          },
          {
            name: '订单增长率',
            type: 'line',
            smooth: true,
            yAxisIndex: 1,
            data: [0, 6.01, 5.26, this.calculateGrowthRate(this.homeData.todayOrder, this.homeData.totalOrder) || 1.48],
            lineStyle: { normal: { width: 2 } },
            itemStyle: { normal: { color: '#86d370' } }
          },
          {
            name: '销售增长率',
            type: 'line',
            yAxisIndex: 1,
            data: [0, -4.73, 6.38, this.calculateGrowthRate(this.homeData.todayPay, this.homeData.totalPay) || 8.67],
            lineStyle: { normal: { width: 2 } },
            itemStyle: { normal: { color: '#3496f8' } },
            smooth: true
          },
          {
            name: '用户增长率',
            type: 'line',
            yAxisIndex: 1,
            data: [0, -1.09, 8.24, this.calculateGrowthRate(this.homeData.todayUser, this.homeData.totalUser) || -9.64],
            lineStyle: { normal: { width: 2 } },
            itemStyle: { normal: { color: '#fbc30d' } },
            smooth: true
          }
        ]
      }
      this.charts.chart1.setOption(option)
      
      window.addEventListener('resize', () => {
        this.charts.chart1.resize();
      });
    },
    // 图表2 - 交易金额趋势（面积图）
    initChart2() {
      this.charts.chart2 = echarts.init(this.$refs.chart2)
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          textStyle: { fontSize: 14 }
        },
        grid: {
          left: '0',
          top: '30',
          right: '10',
          bottom: '-20',
          containLabel: true
        },
        legend: {
          data: ['线上销售', '线下销售'],
          right: 'center',
          top: 0,
          textStyle: { color: '#fff', fontSize: 14 },
          itemWidth: 15,
          itemHeight: 10
        },
        xAxis: [{
          type: 'category',
          boundaryGap: false,
          axisLabel: {
            rotate: -90,
            textStyle: {
              color: 'rgba(255,255,255,.6)',
              fontSize: 14
            }
          },
          axisLine: {
            lineStyle: { color: 'rgba(255,255,255,.1)' }
          },
          data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
        }],
        yAxis: [{
          type: 'value',
          axisTick: { show: false },
          axisLine: {
            lineStyle: { color: 'rgba(255,255,255,.1)' }
          },
          axisLabel: {
            formatter: '{value} %',
            textStyle: {
              color: 'rgba(255,255,255,.6)',
              fontSize: 16
            }
          },
          splitLine: {
            lineStyle: { color: 'rgba(255,255,255,.1)' }
          }
        }],
        series: [
          {
            name: '线上销售',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 5,
            showSymbol: false,
            lineStyle: {
              normal: {
                color: 'rgba(228, 228, 126, 1)',
                width: 2
              }
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'rgba(228, 228, 126, .2)' },
                  { offset: 1, color: 'rgba(228, 228, 126, 0)' }
                ], false),
                shadowColor: 'rgba(0, 0, 0, 0.1)'
              }
            },
            itemStyle: {
              normal: {
                color: 'rgba(228, 228, 126, 1)',
                borderColor: 'rgba(228, 228, 126, .1)',
                borderWidth: 12
              }
            },
            data: this.generateMonthlyData(this.homeData.totalPay, 'positive') || [12.50, 14.4, 16.1, 14.9, 20.1, 17.2, 17.0, 13.42, 20.12, 18.94, 17.27, 16.10]
          },
          {
            name: '线下销售',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 5,
            showSymbol: false,
            lineStyle: {
              normal: {
                color: 'rgba(255, 128, 128, 1)',
                width: 2
              }
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'rgba(255, 128, 128,.2)' },
                  { offset: 1, color: 'rgba(255, 128, 128, 0)' }
                ], false),
                shadowColor: 'rgba(0, 0, 0, 0.1)'
              }
            },
            itemStyle: {
              normal: {
                color: 'rgba(255, 128, 128, 1)',
                borderColor: 'rgba(255, 128, 128, .1)',
                borderWidth: 12
              }
            },
            data: this.generateMonthlyData(this.homeData.totalPay, 'mixed') || [-6.4, 0.1, 6.6, 11.2, 42.1, 26.0, 20.2, 18.31, 21.59, 24.42, 34.03, 32.9]
          }
        ]
      }
      this.charts.chart2.setOption(option)
      
      window.addEventListener('resize', () => {
        this.charts.chart2.resize();
      });
    },
    // 图表3 - 近七日会员活跃数（堆叠柱状图）
    initChart3() {
      this.charts.chart3 = echarts.init(this.$refs.chart3)

      // 使用真实数据
      const xAxisData = this.chartData2.map(item => item.name) || ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
      const activeData = this.chartData2.map(item => item.value) || [48, 97, 56, 59, 90, 98, 164]
      // 模拟新增和流失数据（基于活跃数据的比例）
      const newData = activeData.map(val => Math.floor(val * 1.5))
      const lostData = activeData.map(val => Math.floor(val * 0.3))

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          textStyle: { fontSize: 14 }
        },
        legend: {
          data: ['新增会员', '活跃会员', '流失会员'],
          right: 'center',
          top: 0,
          textStyle: { color: '#fff', fontSize: 14 },
          itemWidth: 15,
          itemHeight: 10
        },
        grid: {
          left: '0',
          right: '20',
          bottom: '0',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisLine: {
            lineStyle: { color: 'rgba(255,255,255,0.3)' }
          },
          axisLabel: {
            formatter: function(value) {
              return value;
            },
            textStyle: {
              color: 'rgba(255,255,255,.6)',
              fontSize: 14
            }
          }
        },
        yAxis: {
          type: 'value',
          splitNumber: 4,
          axisTick: { show: false },
          splitLine: {
            show: true,
            lineStyle: { color: 'rgba(255,255,255,0.1)' }
          },
          axisLabel: {
            textStyle: {
              color: 'rgba(255,255,255,.6)',
              fontSize: 16
            }
          },
          axisLine: { show: false }
        },
        series: [
          {
            name: '新增会员',
            type: 'bar',
            stack: 'a',
            barWidth: '30',
            barGap: 0,
            itemStyle: {
              normal: { color: '#8bd46e' }
            },
            data: newData
          },
          {
            name: '活跃会员',
            type: 'bar',
            stack: 'a',
            barWidth: '30',
            barGap: 0,
            itemStyle: {
              normal: {
                color: '#f5804d',
                barBorderRadius: 0
              }
            },
            data: activeData
          },
          {
            name: '流失会员',
            type: 'bar',
            stack: 'a',
            barWidth: '30',
            barGap: 0,
            itemStyle: {
              normal: {
                color: '#248ff7',
                barBorderRadius: 0
              }
            },
            data: lostData
          }
        ]
      }
      this.charts.chart3.setOption(option)

      window.addEventListener('resize', () => {
        this.charts.chart3.resize();
      });
    },
    // 图表4 - 近七日订单数量（柱状图）
    initChart4() {
      this.charts.chart4 = echarts.init(this.$refs.chart4)

      // 使用真实数据
      const xAxisData = this.chartData1.map(item => item.name) || ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
      const seriesData = this.chartData1.map(item => item.value) || [120, 200, 150, 80, 70, 110, 130]

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          textStyle: { fontSize: 14 }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisLine: {
            lineStyle: { color: 'rgba(255,255,255,.1)' }
          },
          axisLabel: {
            textStyle: {
              color: 'rgba(255,255,255,.6)',
              fontSize: 14
            }
          }
        },
        yAxis: {
          type: 'value',
          axisLine: {
            lineStyle: { color: 'rgba(255,255,255,.1)' }
          },
          axisLabel: {
            textStyle: {
              color: 'rgba(255,255,255,.6)',
              fontSize: 14
            }
          },
          splitLine: {
            lineStyle: { color: 'rgba(255,255,255,.1)' }
          }
        },
        series: [{
          name: '订单数量',
          type: 'bar',
          barWidth: '60%',
          data: seriesData,
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#00d386' },
                { offset: 1, color: '#0093dd' }
              ]),
              barBorderRadius: [5, 5, 0, 0]
            }
          },
          label: {
            show: true,
            position: 'top',
            fontSize: 14,
            color: 'rgba(255,255,255,0.7)'
          }
        }]
      }
      this.charts.chart4.setOption(option)

      window.addEventListener('resize', () => {
        this.charts.chart4.resize();
      });
    },
    // 图表5 - 商品销售排行（横向柱状图）
    initChart5() {
      this.charts.chart5 = echarts.init(this.$refs.chart5)

      // 使用真实数据，取前5个商品
      const topGoods = this.goodsList.slice(0, 5)
      const yAxisData = topGoods.length > 0 ? topGoods.map(item => item.name || `商品${item.id}`).reverse() : ['商品E', '商品D', '商品C', '商品B', '商品A']
      const seriesData = topGoods.length > 0 ? topGoods.map(item => item.num || 0).reverse() : [50, 80, 120, 150, 200]

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          textStyle: { fontSize: 14 }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          axisLine: {
            lineStyle: { color: 'rgba(255,255,255,.1)' }
          },
          axisLabel: {
            textStyle: {
              color: 'rgba(255,255,255,.6)',
              fontSize: 14
            }
          },
          splitLine: {
            lineStyle: { color: 'rgba(255,255,255,.1)' }
          }
        },
        yAxis: {
          type: 'category',
          data: yAxisData,
          axisLine: {
            lineStyle: { color: 'rgba(255,255,255,.1)' }
          },
          axisLabel: {
            textStyle: {
              color: 'rgba(255,255,255,.6)',
              fontSize: 16
            }
          }
        },
        series: [{
          name: '销售量',
          type: 'bar',
          data: seriesData,
          itemStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
                { offset: 0, color: '#fccb05' },
                { offset: 1, color: '#f5804d' }
              ]),
              barBorderRadius: [0, 5, 5, 0]
            }
          },
          label: {
            show: true,
            position: 'right',
            fontSize: 14,
            color: 'rgba(255,255,255,0.7)'
          }
        }]
      }
      this.charts.chart5.setOption(option)

      window.addEventListener('resize', () => {
        this.charts.chart5.resize();
      });
    },
    // 图表6 - 用户分布（饼图）
    initChart6() {
      this.charts.chart6 = echarts.init(this.$refs.chart6)

      // 使用真实数据计算用户分布
      const totalUsers = this.homeData.totalUser || 1000
      const todayUsers = this.homeData.todayUser || 0
      const activeUsers = this.homeData.todayActiveUser || 0
      const payUsers = this.homeData.totalPayUser || 0

      // 计算各类用户数量
      const newUsers = todayUsers
      const oldUsers = totalUsers - todayUsers
      const vipUsers = Math.floor(payUsers * 0.3) // 假设30%的支付用户是VIP
      const normalUsers = totalUsers - vipUsers

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)',
          textStyle: { fontSize: 14 }
        },
        legend: {
          orient: 'vertical',
          left: 10,
          top: 'center',
          textStyle: { color: '#fff', fontSize: 14 },
          data: ['新用户', '老用户', 'VIP用户', '普通用户']
        },
        series: [{
          name: '用户分布',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['65%', '50%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'outside',
            formatter: '{b}: {d}%',
            fontSize: 14,
            color: '#fff'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 18,
              fontWeight: 'bold',
              color: '#fff'
            }
          },
          labelLine: {
            show: false,
            length: 10,
            length2: 10,
            lineStyle: { color: 'rgba(255,255,255,0.3)' }
          },
          data: [
            { value: newUsers, name: '新用户', itemStyle: { color: '#8bd46e' } },
            { value: oldUsers, name: '老用户', itemStyle: { color: '#248ff7' } },
            { value: vipUsers, name: 'VIP用户', itemStyle: { color: '#f5804d' } },
            { value: normalUsers, name: '普通用户', itemStyle: { color: '#fccb05' } }
          ]
        }]
      }
      this.charts.chart6.setOption(option)

      window.addEventListener('resize', () => {
        this.charts.chart6.resize();
      });
    }
  }
}
</script>

<style scoped>
@font-face {
  font-family: electronicFont;
  src: url('~@/assets/fonts/DS-DIGIT.TTF');
}

.bigdata-template {
  width: 100%;
  height: 100vh;
  background: #000d4a url('~@/assets/images/bigdata/bg.jpg') center center;
  background-size: cover;
  color: #fff;
  font-size: 16px;
  overflow: hidden;
  position: relative;
}

.back-button {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1000;
}

.back-button .el-button {
  background: rgba(6, 48, 109, 0.8);
  border-color: #49bcf7;
  color: #fff;
}

.back-button .el-button:hover {
  background: rgba(6, 48, 109, 1);
  border-color: #49bcf7;
}

* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  font-family: "微软雅黑";
}

li {
  list-style-type: none;
}

.clearfix:after,
.clearfix:before {
  display: table;
  content: " ";
}

.clearfix:after {
  clear: both;
}

.loading {
  position: fixed;
  left: 0;
  top: 0;
  font-size: 16px;
  z-index: 100000000;
  width: 100%;
  height: 100%;
  background: #1a1a1c;
  text-align: center;
}

.loadbox {
  position: absolute;
  width: 160px;
  height: 150px;
  color: #324e93;
  left: 50%;
  top: 50%;
  margin-top: -100px;
  margin-left: -75px;
}

.loadbox img {
  margin: 10px auto;
  display: block;
  width: 40px;
}

.head {
  height: 80px;
  background: url('~@/assets/images/bigdata/head_bg.png') no-repeat center center;
  background-size: 100% 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.head h1 {
  color: #fff;
  text-align: center;
  font-size: 36px;
  line-height: 80px;
  letter-spacing: 2px;
  font-weight: bold;
  text-shadow: 0 0 15px rgba(0, 100, 255, 0.5);
}

.time {
  position: absolute;
  right: 30px;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.9);
  font-size: 28px;
  padding-right: 10px;
  font-family: electronicFont;
  text-shadow: 0 0 10px rgba(73, 188, 247, 0.8);
}

.mainbox {
  padding: 10px 20px 10px 20px;
  height: calc(100% - 80px);
}

.mainbox > ul {
  margin-left: -10px;
  margin-right: -10px;
  height: 100%;
}

.mainbox > ul > li {
  float: left;
  padding: 0 10px;
  height: 100%;
  width: 30%;
}

.mainbox > ul > li:nth-child(2) {
  width: 40%;
}

.boxall {
  padding: 10px 20px 20px 20px;
  background: rgba(6, 48, 109, 0.5);
  position: relative;
  margin-bottom: 15px;
  z-index: 10;
  border: 1px solid rgba(73, 188, 247, 0.3);
  box-shadow: 0 0 10px rgba(73, 188, 247, 0.2);
  border-radius: 5px;
}

.alltitle {
  font-size: 18px;
  color: #fff;
  line-height: 40px;
  position: relative;
  padding-left: 15px;
  margin-top: -10px;
  font-weight: bold;
  text-shadow: 0 0 5px rgba(73, 188, 247, 0.5);
}

.alltitle:before {
  position: absolute;
  height: 20px;
  width: 4px;
  background: #49bcf7;
  border-radius: 5px;
  content: "";
  left: 0;
  top: 50%;
  margin-top: -10px;
  box-shadow: 0 0 5px rgba(73, 188, 247, 0.8);
}

.boxnav {
  height: calc(100% - 0px);
}

.row > li {
  float: left;
  height: 100%;
}

.col-6 {
  width: 50%;
}

.col-4 {
  width: 33.33333%;
}

.h100 {
  height: 100%;
}

.tit01 {
  text-align: center;
  color: white;
  font-size: 16px;
  padding: 30px 0 2px 0;
  font-weight: bold;
  margin-top: -35px;
}

.piebox {
  height: calc(80% - 50px);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top:30px;
}

.piebox:before {
  width: 60px;
  height: 60px;
  content: "";
  border: 1px solid #49bcf7;
  border-radius: 100px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -31px;
  margin-top: -31px;
  opacity: 0.7;
  box-shadow: 0 0 15px rgba(73, 188, 247, 0.5);
}

.pie-number {
  font-size: 20px;
  color: #49bcf7;
  font-weight: bold;
  z-index: 2;
  position: relative;
}

.sqzs {
  margin-right: 0px;
}

.sqzs p {
  padding: 20px 0 10px 0;
  font-size: 22px;
  text-align: center;
  font-weight: bold;
}

.sqzs h1 {
  height: calc(100% - 65px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  color: #fef000;
  font-weight: normal;
  letter-spacing: 2px;
  font-size: 50px;
  justify-content: center;
  padding-bottom: 5px;
  text-shadow: 0 0 10px rgba(254, 240, 0, 0.5);
}

.sqzs h1 span {
  font-size: 80px;
  font-family: Impact, Haettenschweiler, 'Arial Narrow Bold', sans-serif;
}

table {
  width: 100%;
  text-align: center;
  border-collapse: collapse;
  box-shadow: 0 0 10px rgba(73, 188, 247, 0.2);
  margin-top: 5px;
}

table th {
  font-size: 18px;
  background: rgba(0, 0, 0, 0.3);
  padding: 8px 5px;
  color: #fff;
  font-weight: bold;
}

table td {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9);
  padding: 8px 5px;
}

table th,
table td {
  border-bottom: 1px solid rgba(73, 188, 247, 0.3);
}

/* 为数据统计表添加特定样式 */
.boxall:nth-child(2) .boxnav table {
  font-size: 16px;
}

.boxall:nth-child(2) .boxnav table th,
.boxall:nth-child(2) .boxnav table td {
  padding: 6px 3px;
  font-size: 16px;
}

@media screen and (max-width: 1600px) {
  .head h1 {
    font-size: 32px;
  }
  
  .alltitle {
    font-size: 16px;
  }
  
  .time {
    font-size: 24px;
  }
  
  table th {
    font-size: 16px;
  }
  
  table td {
    font-size: 16px;
  }
  
  .sqzs h1 {
    font-size: 36px;
  }
  
  .sqzs h1 span {
    font-size: 60px;
  }
}

@media screen and (max-width: 1200px) {
  .head h1 {
    font-size: 28px;
  }
  
  .time {
    font-size: 20px;
  }
  
  .alltitle {
    font-size: 14px;
  }
  
  table th, table td {
    font-size: 14px;
    padding: 8px 2px;
  }
  
  .sqzs h1 {
    font-size: 30px;
  }
  
  .sqzs h1 span {
    font-size: 50px;
  }
  
  .pie-number {
    font-size: 20px;
  }
}
</style>
